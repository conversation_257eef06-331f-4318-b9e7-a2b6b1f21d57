# 成分效应和电场效应计算方法

## 理论基础

**方差分解（Variance Decomposition）**：
```
总变异 = 组间变异 + 组内变异
```

在我们的分析中：
- **组间变异** = 成分效应（不同成分之间的差异）
- **组内变异** = 电场效应（同一成分在不同电场下的差异）
- **每个组** = 同一篇文献 + 同一个成分

## 计算步骤

### 步骤1: 计算组间变异（成分效应）

1. 计算每个组的均值：
   ```python
   group_means = df.groupby('group_id')[metric].mean()
   ```

2. 计算组间变异：
   ```python
   between_group_var = group_means.var()
   ```
   
   **含义**：各组均值之间的方差，反映不同成分之间的差异

### 步骤2: 计算组内变异（电场效应）

1. 计算每个组内的方差：
   ```python
   for group in df['group_id'].unique():
       group_data = df[df['group_id'] == group][metric]
       if len(group_data) > 1:
           within_group_vars.append(group_data.var())
   ```

2. 计算平均组内变异：
   ```python
   within_group_var = np.mean(within_group_vars)
   ```
   
   **含义**：同一成分内部数据的平均方差，反映电场变化的影响

### 步骤3: 计算效应比例

```python
total_var = between_group_var + within_group_var
component_effect = between_group_var / total_var  # 成分效应占比
field_effect = within_group_var / total_var       # 电场效应占比
```

## 实际例子（以Wrec/Eb为例）

### 计算结果：
- 组间变异（成分效应）：0.000016
- 组内变异（电场效应）：0.000011
- 总变异：0.000028

### 效应比例：
- 成分效应占比：0.000016 / 0.000028 = 59.24%
- 电场效应占比：0.000011 / 0.000028 = 40.76%

### 结论：
Wrec/Eb 主要由成分决定，因为不同成分之间的差异（59.24%）比同一成分在不同电场下的差异（40.76%）更大。

## 结果解释

### 如果成分效应 > 电场效应：
- 说明该指标主要由材料成分决定
- 不同成分之间的差异比电场变化引起的差异更大
- 材料设计时应重点关注成分选择

### 如果电场效应 > 成分效应：
- 说明该指标主要由电场强度决定
- 电场变化引起的差异比成分差异更大
- 性能优化时应重点关注电场调节

## 注意事项

1. **数据要求**：需要有足够的组，且每组有多个数据点
2. **假设条件**：假设组内方差相等（同质性假设）
3. **解释限制**：只能说明相对重要性，不能说明绝对因果关系
4. **统计意义**：这是描述性统计分析，不是推断性统计检验
