import pandas as pd
import numpy as np

print("="*80)
print("差异比率计算公式总结")
print("="*80)

print("\n📐 数学公式:")
print("   差异比率 = CV_between / CV_within")
print("   其中:")
print("   CV_between = σ_between / μ_between")
print("   CV_within = (1/n) × Σ(σ_i / μ_i)")
print("   符号说明:")
print("   - CV: 变异系数 (Coefficient of Variation)")
print("   - σ: 标准差")
print("   - μ: 平均值")
print("   - n: 成分数量")
print("   - i: 第i个成分")

print("\n🔢 具体计算步骤:")

# 读取数据进行实际计算演示
df = pd.read_excel('data2024_copy.xlsx')
df_clean = df.dropna()
df_clean = df_clean.copy()
df_clean['Wrec_normalized'] = 10000 * df_clean['Wrec (J cm-3)'] / (df_clean['E (kV cm-1)'] ** 2)

component_test_counts = df_clean.groupby('component').size()
components_with_multiple_tests = component_test_counts[component_test_counts >= 3].index
df_analysis = df_clean[df_clean['component'].isin(components_with_multiple_tests)].copy()

print("\n步骤1: 计算每个成分的平均值")
component_means = df_analysis.groupby('component')['Wrec_normalized'].mean()
print(f"   得到 {len(component_means)} 个成分的平均值")

print("\n步骤2: 计算不同成分间的变异系数 (CV_between)")
mu_between = component_means.mean()
sigma_between = component_means.std()
cv_between = sigma_between / mu_between
print(f"   μ_between = {mu_between:.4f}")
print(f"   σ_between = {sigma_between:.4f}")
print(f"   CV_between = {sigma_between:.4f} / {mu_between:.4f} = {cv_between:.4f}")

print("\n步骤3: 计算每个成分内部的变异系数")
within_cvs = []
for component in components_with_multiple_tests:
    component_data = df_analysis[df_analysis['component'] == component]
    mu_i = component_data['Wrec_normalized'].mean()
    sigma_i = component_data['Wrec_normalized'].std()
    if mu_i != 0:
        cv_i = sigma_i / mu_i
        within_cvs.append(cv_i)

print(f"   计算了 {len(within_cvs)} 个成分的内部变异系数")

print("\n步骤4: 计算相同成分内平均变异系数 (CV_within)")
cv_within = np.mean(within_cvs)
print(f"   CV_within = (1/{len(within_cvs)}) × Σ(CV_i) = {cv_within:.4f}")

print("\n步骤5: 计算差异比率")
ratio = cv_between / cv_within
print(f"   差异比率 = {cv_between:.4f} / {cv_within:.4f} = {ratio:.4f}")

print("\n📊 结果解释:")
print(f"   差异比率 = {ratio:.2f}")
if ratio > 2:
    print("   ✅ 比率 > 2: 成分效应非常显著")
elif ratio > 1:
    print("   ✅ 比率 > 1: 成分效应显著")
elif ratio > 0.5:
    print("   ⚠️  0.5 < 比率 < 1: 成分效应不明显")
else:
    print("   ❌ 比率 < 0.5: 成分效应很弱")

print("\n🎯 物理意义:")
print("   - 差异比率衡量'成分间变异'相对于'成分内变异'的大小")
print("   - 比率越大，说明成分对性能的影响越重要")
print("   - 比率接近1，说明成分效应与随机误差相当")
print("   - 比率远大于1，说明成分是主要影响因素")

print("\n📈 实际应用:")
print("   在材料科学中，这个比率帮助我们判断:")
print("   1. 成分设计是否比工艺条件更重要")
print("   2. 实验中观察到的差异是否具有统计学意义")
print("   3. 不同研究结果的可比性")

print("\n" + "="*80)
print("总结: 差异比率是一个标准化的统计指标")
print("用于量化分组因素(成分)相对于随机变异的影响强度")
print("="*80)
