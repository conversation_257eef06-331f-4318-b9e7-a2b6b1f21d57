import numpy as np
import pandas as pd
from sklearn.model_selection import GroupKFold, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
import warnings
warnings.filterwarnings('ignore')


class ModelSelector:
    """模型选择和超参数优化类"""
    
    def __init__(self):
        self.scaler = StandardScaler()
        self.models = self._initialize_models()
        self.param_grids = self._initialize_param_grids()
        
    def _initialize_models(self):
        """初始化基础模型"""
        return {
            'Random Forest': RandomForestRegressor(random_state=42),
            'Gradient Boosting': GradientBoostingRegressor(random_state=42),
            'Linear Regression': LinearRegression()
        }
    
    def _initialize_param_grids(self):
        """初始化超参数网格"""
        return {
            'Random Forest': {
                'n_estimators': [50, 100, 200],
                'max_depth': [None, 10, 20, 30],
                'min_samples_split': [2, 5, 10],
                'min_samples_leaf': [1, 2, 4]
            },
            'Gradient Boosting': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7],
                'min_samples_split': [2, 5, 10]
            },
            'Linear Regression': {}  # 线性回归没有超参数需要调优
        }
    
    def train_models_with_cv(self, X_train_val, y_train_val, groups_train_val, target_name, 
                           use_hyperparameter_tuning=False):
        """使用交叉验证训练多个模型并选择最佳模型"""
        print(f"\n正在使用交叉验证训练{target_name}预测模型...")
        
        # 使用GroupKFold确保同一组不会同时出现在训练和验证中
        group_kfold = GroupKFold(n_splits=10)
        
        best_score = -np.inf
        best_model_name = None
        cv_results = {}
        
        for name, base_model in self.models.items():
            print(f"训练 {name}...")
            
            if use_hyperparameter_tuning and name in self.param_grids and self.param_grids[name]:
                # 使用网格搜索进行超参数优化
                model = self._hyperparameter_tuning(
                    base_model, name, X_train_val, y_train_val, groups_train_val
                )
            else:
                # 使用默认参数
                model = base_model
            
            # 执行交叉验证
            if name == 'Linear Regression':
                # 创建管道包含标准化和模型
                pipeline = Pipeline([
                    ('scaler', StandardScaler()),
                    ('model', model)
                ])
                
                cv_scores = cross_val_score(
                    pipeline, X_train_val, y_train_val,
                    groups=groups_train_val, cv=group_kfold,
                    scoring='r2', n_jobs=-1
                )
            else:
                # 对于树模型，直接使用原始特征
                cv_scores = cross_val_score(
                    model, X_train_val, y_train_val,
                    groups=groups_train_val, cv=group_kfold,
                    scoring='r2', n_jobs=-1
                )
            
            # 计算交叉验证统计
            mean_score = cv_scores.mean()
            std_score = cv_scores.std()
            
            cv_results[name] = {
                'cv_scores': cv_scores,
                'mean_r2': mean_score,
                'std_r2': std_score,
                'model': model
            }
            
            print(f"  交叉验证R²: {mean_score:.4f} (±{std_score:.4f})")
            
            # 选择最佳模型（基于平均R²分数）
            if mean_score > best_score:
                best_score = mean_score
                best_model_name = name
        
        print(f"\n最佳模型: {best_model_name} (CV R² = {best_score:.4f})")
        
        # 使用最佳模型在全部训练+验证数据上重新训练
        best_model = cv_results[best_model_name]['model']
        
        if best_model_name == 'Linear Regression':
            # 标准化特征
            X_train_val_scaled = self.scaler.fit_transform(X_train_val)
            best_model.fit(X_train_val_scaled, y_train_val)
        else:
            best_model.fit(X_train_val, y_train_val)
        
        return cv_results, best_model_name, best_model
    
    def _hyperparameter_tuning(self, model, model_name, X, y, groups):
        """超参数调优"""
        print(f"  正在进行{model_name}超参数调优...")
        
        param_grid = self.param_grids[model_name]
        group_kfold = GroupKFold(n_splits=5)  # 减少折数以加快速度
        
        if model_name == 'Linear Regression':
            # 线性回归使用管道
            pipeline = Pipeline([
                ('scaler', StandardScaler()),
                ('model', model)
            ])
            # 为管道调整参数网格
            param_grid = {f'model__{k}': v for k, v in param_grid.items()}
            
            grid_search = GridSearchCV(
                pipeline, param_grid, cv=group_kfold,
                scoring='r2', n_jobs=-1, verbose=0
            )
        else:
            grid_search = GridSearchCV(
                model, param_grid, cv=group_kfold,
                scoring='r2', n_jobs=-1, verbose=0
            )
        
        # 执行网格搜索
        grid_search.fit(X, y, groups=groups)
        
        print(f"  最佳参数: {grid_search.best_params_}")
        print(f"  最佳CV分数: {grid_search.best_score_:.4f}")
        
        return grid_search.best_estimator_
    
    def evaluate_model(self, model, X_test, y_test, model_name, target_name, use_scaling=False):
        """评估模型在测试集上的性能"""
        print(f"\n评估{target_name}预测模型 ({model_name}) 在测试集上的性能...")
        
        if use_scaling:
            X_test_scaled = self.scaler.transform(X_test)
            y_pred = model.predict(X_test_scaled)
        else:
            y_pred = model.predict(X_test)
        
        mse = mean_squared_error(y_test, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test, y_pred)
        r2 = r2_score(y_test, y_pred)
        
        print(f"测试集性能:")
        print(f"  RMSE: {rmse:.4f}")
        print(f"  MAE: {mae:.4f}")
        print(f"  R²: {r2:.4f}")
        
        return {'rmse': rmse, 'mae': mae, 'r2': r2, 'predictions': y_pred}
    
    def get_feature_importance(self, model, feature_names):
        """获取特征重要性"""
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
            feature_importance = dict(zip(feature_names, importances))
            return feature_importance
        elif hasattr(model, 'coef_'):
            # 对于线性模型，使用系数的绝对值作为重要性
            importances = np.abs(model.coef_)
            feature_importance = dict(zip(feature_names, importances))
            return feature_importance
        else:
            return None
    
    def compare_models(self, cv_results):
        """比较不同模型的性能"""
        print("\n模型性能比较:")
        print("-" * 50)
        
        for name, results in cv_results.items():
            mean_r2 = results['mean_r2']
            std_r2 = results['std_r2']
            print(f"{name:20s}: R² = {mean_r2:.4f} (±{std_r2:.4f})")
        
        # 找出最佳模型
        best_model = max(cv_results.items(), key=lambda x: x[1]['mean_r2'])
        print(f"\n最佳模型: {best_model[0]} (R² = {best_model[1]['mean_r2']:.4f})")
        
        return best_model[0], best_model[1]
