import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体 - 修复Windows中文显示问题
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_explore_data(file_path):
    """加载数据并进行初步探索"""
    print("正在加载数据...")
    df = pd.read_excel(file_path)
    
    print(f"数据形状: {df.shape}")
    print("\n列名:")
    print(df.columns.tolist())
    print("\n前5行数据:")
    print(df.head())
    print("\n数据类型:")
    print(df.dtypes)
    print("\n缺失值统计:")
    print(df.isnull().sum())
    
    return df

def calculate_metrics(df):
    """计算Wrec/Eb和Wrec/(Eb*Eb)指标"""
    # 重命名列以便于处理
    df = df.rename(columns={
        'E (kV cm-1)': 'Eb',
        'Wrec (J cm-3)': 'Wrec'
    })

    # 检查必要的列是否存在
    required_cols = ['Wrec', 'Eb']
    missing_cols = [col for col in required_cols if col not in df.columns]

    if missing_cols:
        print(f"缺少必要的列: {missing_cols}")
        print("可用的列:", df.columns.tolist())
        return df

    # 移除缺失值
    df = df.dropna(subset=['Wrec', 'Eb'])

    # 计算指标
    df['Wrec_over_Eb'] = df['Wrec'] / df['Eb']
    df['Wrec_over_Eb_squared'] = df['Wrec'] / (df['Eb'] ** 2)

    print("已计算指标:")
    print("- Wrec/Eb")
    print("- Wrec/(Eb*Eb)")
    print(f"有效数据点: {len(df)}")

    return df

def identify_groups(df):
    """识别来自同一篇文献同一个成分的数据组"""
    # 根据实际数据结构，使用DOI和component作为分组依据
    literature_cols = ['DOI']
    composition_cols = ['component']

    print("文献相关列:", literature_cols)
    print("成分相关列:", composition_cols)

    # 创建组合分组列
    df['group_id'] = df['DOI'].astype(str) + '_' + df['component'].astype(str)

    # 统计每组的数据点数量
    group_counts = df['group_id'].value_counts()
    print(f"\n总共有 {len(group_counts)} 个不同的组")
    print(f"每组数据点数量统计:")
    print(f"  最少: {group_counts.min()} 个数据点")
    print(f"  最多: {group_counts.max()} 个数据点")
    print(f"  平均: {group_counts.mean():.1f} 个数据点")
    print(f"  有>=3个数据点的组: {(group_counts >= 3).sum()} 个")

    # 显示一些示例组
    print(f"\n前10个组的数据点数量:")
    print(group_counts.head(10))

    return literature_cols, composition_cols

def analyze_group_consistency(df):
    """分析组内指标的一致性"""
    if 'group_id' not in df.columns:
        print("请先运行identify_groups函数")
        return None, None

    # 只分析有多个数据点的组
    group_counts = df['group_id'].value_counts()
    multi_point_groups = group_counts[group_counts >= 2].index
    df_multi = df[df['group_id'].isin(multi_point_groups)]

    print(f"\n=== 组内一致性分析 ===")
    print(f"分析 {len(multi_point_groups)} 个有多个数据点的组")

    # 分析每组的统计信息
    group_stats = df_multi.groupby('group_id').agg({
        'Wrec_over_Eb': ['count', 'mean', 'std', 'min', 'max'],
        'Wrec_over_Eb_squared': ['count', 'mean', 'std', 'min', 'max'],
        'Wrec': ['mean', 'std', 'min', 'max'],
        'Eb': ['mean', 'std', 'min', 'max']
    }).round(4)

    print("\n各组统计信息 (前10组):")
    print(group_stats.head(10))

    # 计算变异系数(CV = std/mean)
    cv_analysis = df_multi.groupby('group_id').apply(lambda x: pd.Series({
        'CV_Wrec_over_Eb': x['Wrec_over_Eb'].std() / x['Wrec_over_Eb'].mean() if x['Wrec_over_Eb'].mean() != 0 else np.nan,
        'CV_Wrec_over_Eb_squared': x['Wrec_over_Eb_squared'].std() / x['Wrec_over_Eb_squared'].mean() if x['Wrec_over_Eb_squared'].mean() != 0 else np.nan,
        'CV_Wrec': x['Wrec'].std() / x['Wrec'].mean() if x['Wrec'].mean() != 0 else np.nan,
        'CV_Eb': x['Eb'].std() / x['Eb'].mean() if x['Eb'].mean() != 0 else np.nan,
        'sample_count': len(x)
    })).round(4)

    print("\n各组变异系数(CV)分析 (前10组):")
    print(cv_analysis.head(10))

    # 总体统计
    print(f"\n=== 总体变异系数统计 ===")
    print(f"Wrec/Eb 变异系数: 平均={cv_analysis['CV_Wrec_over_Eb'].mean():.4f}, 中位数={cv_analysis['CV_Wrec_over_Eb'].median():.4f}")
    print(f"Wrec/Eb² 变异系数: 平均={cv_analysis['CV_Wrec_over_Eb_squared'].mean():.4f}, 中位数={cv_analysis['CV_Wrec_over_Eb_squared'].median():.4f}")
    print(f"Wrec 变异系数: 平均={cv_analysis['CV_Wrec'].mean():.4f}, 中位数={cv_analysis['CV_Wrec'].median():.4f}")
    print(f"Eb 变异系数: 平均={cv_analysis['CV_Eb'].mean():.4f}, 中位数={cv_analysis['CV_Eb'].median():.4f}")

    return group_stats, cv_analysis

def visualize_group_analysis(df):
    """可视化组内分析结果"""
    if 'group_id' not in df.columns:
        print("请先运行analyze_group_consistency函数")
        return

    # 只分析样本数>=3的组
    group_counts = df['group_id'].value_counts()
    valid_groups = group_counts[group_counts >= 3].index
    df_filtered = df[df['group_id'].isin(valid_groups)]

    if len(df_filtered) == 0:
        print("没有足够的组数据进行可视化分析")
        return

    plt.style.use('default')
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. Wrec/Eb的组内分布
    axes[0,0].boxplot([df_filtered[df_filtered['group_id']==group]['Wrec_over_Eb'].values 
                      for group in valid_groups[:10]], labels=valid_groups[:10])
    axes[0,0].set_title('Wrec/Eb 组内分布')
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # 2. Wrec/(Eb*Eb)的组内分布
    axes[0,1].boxplot([df_filtered[df_filtered['group_id']==group]['Wrec_over_Eb_squared'].values 
                      for group in valid_groups[:10]], labels=valid_groups[:10])
    axes[0,1].set_title('Wrec/(Eb*Eb) 组内分布')
    axes[0,1].tick_params(axis='x', rotation=45)
    
    # 3. Wrec vs Eb散点图，按组着色
    for i, group in enumerate(valid_groups[:10]):
        group_data = df_filtered[df_filtered['group_id']==group]
        axes[1,0].scatter(group_data['Eb'], group_data['Wrec'], 
                         label=f'Group {i+1}', alpha=0.7)
    axes[1,0].set_xlabel('Eb')
    axes[1,0].set_ylabel('Wrec')
    axes[1,0].set_title('Wrec vs Eb (按组分色)')
    axes[1,0].legend()
    
    # 4. 变异系数比较
    cv_data = df_filtered.groupby('group_id').apply(lambda x: pd.Series({
        'CV_Wrec_over_Eb': x['Wrec_over_Eb'].std() / x['Wrec_over_Eb'].mean(),
        'CV_Wrec_over_Eb_squared': x['Wrec_over_Eb_squared'].std() / x['Wrec_over_Eb_squared'].mean()
    }))
    
    x_pos = np.arange(len(cv_data))
    width = 0.35
    axes[1,1].bar(x_pos - width/2, cv_data['CV_Wrec_over_Eb'], width, label='CV(Wrec/Eb)')
    axes[1,1].bar(x_pos + width/2, cv_data['CV_Wrec_over_Eb_squared'], width, label='CV(Wrec/Eb²)')
    axes[1,1].set_xlabel('组别')
    axes[1,1].set_ylabel('变异系数')
    axes[1,1].set_title('各组指标变异系数比较')
    axes[1,1].legend()
    axes[1,1].set_xticks(x_pos)
    axes[1,1].set_xticklabels([f'G{i+1}' for i in range(len(cv_data))])
    
    plt.tight_layout()
    plt.show()

def analyze_component_vs_field_effect(df):
    """分析成分效应vs电场效应"""
    print("\n=== 成分效应 vs 电场效应分析 ===")
    
    if 'group_id' not in df.columns:
        print("请先运行analyze_group_consistency函数")
        return
    
    # 计算组间和组内变异
    results = {}
    
    for metric in ['Wrec', 'Wrec_over_Eb', 'Wrec_over_Eb_squared']:
        # 组间变异 (成分效应)
        group_means = df.groupby('group_id')[metric].mean()
        between_group_var = group_means.var()
        
        # 组内变异 (电场效应)
        within_group_vars = []
        for group in df['group_id'].unique():
            group_data = df[df['group_id'] == group][metric]
            if len(group_data) > 1:
                within_group_vars.append(group_data.var())
        
        within_group_var = np.mean(within_group_vars) if within_group_vars else 0
        
        # 计算效应比例
        total_var = between_group_var + within_group_var
        component_effect = between_group_var / total_var if total_var > 0 else 0
        field_effect = within_group_var / total_var if total_var > 0 else 0
        
        results[metric] = {
            'between_group_var': between_group_var,
            'within_group_var': within_group_var,
            'component_effect_ratio': component_effect,
            'field_effect_ratio': field_effect
        }
        
        print(f"\n{metric}:")
        print(f"  组间变异 (成分效应): {between_group_var:.6f}")
        print(f"  组内变异 (电场效应): {within_group_var:.6f}")
        print(f"  成分效应占比: {component_effect:.2%}")
        print(f"  电场效应占比: {field_effect:.2%}")
    
    return results

def create_detailed_visualizations(df, cv_analysis, effect_results):
    """创建详细的可视化分析"""
    plt.style.use('default')

    # 1. 变异系数分布图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # CV分布直方图
    axes[0,0].hist(cv_analysis['CV_Wrec_over_Eb'].dropna(), bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_title('Wrec/Eb 变异系数分布')
    axes[0,0].set_xlabel('变异系数')
    axes[0,0].set_ylabel('频数')
    axes[0,0].axvline(cv_analysis['CV_Wrec_over_Eb'].median(), color='red', linestyle='--', label=f'中位数: {cv_analysis["CV_Wrec_over_Eb"].median():.3f}')
    axes[0,0].legend()

    axes[0,1].hist(cv_analysis['CV_Wrec_over_Eb_squared'].dropna(), bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0,1].set_title('Wrec/Eb² 变异系数分布')
    axes[0,1].set_xlabel('变异系数')
    axes[0,1].set_ylabel('频数')
    axes[0,1].axvline(cv_analysis['CV_Wrec_over_Eb_squared'].median(), color='red', linestyle='--', label=f'中位数: {cv_analysis["CV_Wrec_over_Eb_squared"].median():.3f}')
    axes[0,1].legend()

    # 成分效应vs电场效应对比
    metrics = list(effect_results.keys())
    component_effects = [effect_results[m]['component_effect_ratio'] for m in metrics]
    field_effects = [effect_results[m]['field_effect_ratio'] for m in metrics]

    x = np.arange(len(metrics))
    width = 0.35

    axes[1,0].bar(x - width/2, component_effects, width, label='成分效应', color='orange', alpha=0.8)
    axes[1,0].bar(x + width/2, field_effects, width, label='电场效应', color='purple', alpha=0.8)
    axes[1,0].set_xlabel('指标')
    axes[1,0].set_ylabel('效应占比')
    axes[1,0].set_title('成分效应 vs 电场效应')
    axes[1,0].set_xticks(x)
    axes[1,0].set_xticklabels([m.replace('_', '/') for m in metrics], rotation=45)
    axes[1,0].legend()
    axes[1,0].set_ylim(0, 1)

    # 散点图：Wrec vs Eb，按组着色
    group_counts = df['group_id'].value_counts()
    top_groups = group_counts.head(10).index
    colors = plt.cm.tab10(np.linspace(0, 1, len(top_groups)))

    for i, group in enumerate(top_groups):
        group_data = df[df['group_id'] == group]
        axes[1,1].scatter(group_data['Eb'], group_data['Wrec'],
                         color=colors[i], alpha=0.7, s=30, label=f'组{i+1}')

    axes[1,1].set_xlabel('Eb (kV/cm)')
    axes[1,1].set_ylabel('Wrec (J/cm³)')
    axes[1,1].set_title('Wrec vs Eb (前10个最大组)')
    axes[1,1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    plt.tight_layout()
    plt.savefig('data_analysis_results.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 创建组内一致性分析图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    # 选择有足够数据点的组进行箱线图分析
    valid_groups = group_counts[group_counts >= 5].head(8).index

    # Wrec/Eb 箱线图
    data_for_box1 = [df[df['group_id']==group]['Wrec_over_Eb'].values for group in valid_groups]
    bp1 = axes[0].boxplot(data_for_box1, labels=[f'G{i+1}' for i in range(len(valid_groups))])
    axes[0].set_title('Wrec/Eb 组内分布')
    axes[0].set_ylabel('Wrec/Eb')
    axes[0].tick_params(axis='x', rotation=45)

    # Wrec/Eb² 箱线图
    data_for_box2 = [df[df['group_id']==group]['Wrec_over_Eb_squared'].values for group in valid_groups]
    bp2 = axes[1].boxplot(data_for_box2, labels=[f'G{i+1}' for i in range(len(valid_groups))])
    axes[1].set_title('Wrec/Eb² 组内分布')
    axes[1].set_ylabel('Wrec/Eb²')
    axes[1].tick_params(axis='x', rotation=45)

    # 变异系数对比
    cv_comparison = cv_analysis.loc[valid_groups, ['CV_Wrec_over_Eb', 'CV_Wrec_over_Eb_squared']].fillna(0)
    x_pos = np.arange(len(cv_comparison))
    width = 0.35

    axes[2].bar(x_pos - width/2, cv_comparison['CV_Wrec_over_Eb'], width,
               label='CV(Wrec/Eb)', color='skyblue', alpha=0.8)
    axes[2].bar(x_pos + width/2, cv_comparison['CV_Wrec_over_Eb_squared'], width,
               label='CV(Wrec/Eb²)', color='lightgreen', alpha=0.8)
    axes[2].set_xlabel('组别')
    axes[2].set_ylabel('变异系数')
    axes[2].set_title('各组变异系数对比')
    axes[2].set_xticks(x_pos)
    axes[2].set_xticklabels([f'G{i+1}' for i in range(len(cv_comparison))])
    axes[2].legend()

    plt.tight_layout()
    plt.savefig('group_consistency_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("可视化图表已保存为:")
    print("- data_analysis_results.png")
    print("- group_consistency_analysis.png")

def detailed_verification(df, cv_analysis):
    """详细验证分析结果"""
    print("\n" + "="*60)
    print("=== 详细验证分析 ===")
    print("="*60)

    # 选择几个典型的组进行详细分析
    group_counts = df['group_id'].value_counts()
    large_groups = group_counts[group_counts >= 10].head(3).index

    for i, group in enumerate(large_groups):
        print(f"\n--- 组 {i+1}: {group[:50]}... ---")
        group_data = df[df['group_id'] == group]

        print(f"数据点数: {len(group_data)}")
        print(f"Eb 范围: {group_data['Eb'].min():.1f} - {group_data['Eb'].max():.1f}")
        print(f"Wrec 范围: {group_data['Wrec'].min():.2f} - {group_data['Wrec'].max():.2f}")

        # 计算各指标的变异系数
        cv_wrec = group_data['Wrec'].std() / group_data['Wrec'].mean()
        cv_eb = group_data['Eb'].std() / group_data['Eb'].mean()
        cv_wrec_eb = group_data['Wrec_over_Eb'].std() / group_data['Wrec_over_Eb'].mean()
        cv_wrec_eb2 = group_data['Wrec_over_Eb_squared'].std() / group_data['Wrec_over_Eb_squared'].mean()

        print(f"变异系数:")
        print(f"  Wrec: {cv_wrec:.4f}")
        print(f"  Eb: {cv_eb:.4f}")
        print(f"  Wrec/Eb: {cv_wrec_eb:.4f}")
        print(f"  Wrec/Eb²: {cv_wrec_eb2:.4f}")

        # 显示前5个数据点
        print(f"前5个数据点:")
        sample_data = group_data[['Eb', 'Wrec', 'Wrec_over_Eb', 'Wrec_over_Eb_squared']].head()
        print(sample_data.round(4))

    # 总体统计验证
    print(f"\n--- 总体统计验证 ---")
    print(f"所有组的变异系数分布:")
    print(f"Wrec/Eb 变异系数:")
    print(f"  最小值: {cv_analysis['CV_Wrec_over_Eb'].min():.4f}")
    print(f"  25%分位数: {cv_analysis['CV_Wrec_over_Eb'].quantile(0.25):.4f}")
    print(f"  中位数: {cv_analysis['CV_Wrec_over_Eb'].median():.4f}")
    print(f"  75%分位数: {cv_analysis['CV_Wrec_over_Eb'].quantile(0.75):.4f}")
    print(f"  最大值: {cv_analysis['CV_Wrec_over_Eb'].max():.4f}")

    print(f"\nWrec/Eb² 变异系数:")
    print(f"  最小值: {cv_analysis['CV_Wrec_over_Eb_squared'].min():.4f}")
    print(f"  25%分位数: {cv_analysis['CV_Wrec_over_Eb_squared'].quantile(0.25):.4f}")
    print(f"  中位数: {cv_analysis['CV_Wrec_over_Eb_squared'].median():.4f}")
    print(f"  75%分位数: {cv_analysis['CV_Wrec_over_Eb_squared'].quantile(0.75):.4f}")
    print(f"  最大值: {cv_analysis['CV_Wrec_over_Eb_squared'].max():.4f}")

def main():
    """主函数"""
    print("=== 数据分析开始 ===")

    # 1. 加载数据
    df = load_and_explore_data('data2024_copy.xlsx')

    # 2. 计算指标
    df = calculate_metrics(df)

    # 3. 识别分组
    identify_groups(df)

    # 4. 分析组内一致性
    group_stats, cv_analysis = analyze_group_consistency(df)

    # 5. 分析成分效应vs电场效应
    effect_results = analyze_component_vs_field_effect(df)

    # 6. 详细验证分析
    if cv_analysis is not None:
        detailed_verification(df, cv_analysis)

    # 7. 可视化分析
    try:
        create_detailed_visualizations(df, cv_analysis, effect_results)
    except Exception as e:
        print(f"可视化过程中出现错误: {e}")

    # 8. 总结分析结果
    print("\n" + "="*50)
    print("=== 分析结论 ===")
    print("="*50)

    if cv_analysis is not None:
        # 判断指标是否在组内一致
        cv_wrec_eb = cv_analysis['CV_Wrec_over_Eb'].median()
        cv_wrec_eb2 = cv_analysis['CV_Wrec_over_Eb_squared'].median()

        print(f"\n1. 组内一致性分析 (变异系数越小越稳定):")
        print(f"   - Wrec/Eb 组内变异系数中位数: {cv_wrec_eb:.4f}")
        print(f"   - Wrec/Eb² 组内变异系数中位数: {cv_wrec_eb2:.4f}")

        # 比较两个指标的稳定性
        if cv_wrec_eb2 < cv_wrec_eb:
            print(f"   - Wrec/Eb² 比 Wrec/Eb 在组内更稳定 (变异系数更小)")
            print(f"   - 这说明 Wrec/Eb² 受电场变化影响更小")
        else:
            print(f"   - Wrec/Eb 比 Wrec/Eb² 在组内更稳定 (变异系数更小)")
            print(f"   - 这说明 Wrec/Eb 受电场变化影响更小")

        # 绝对稳定性判断
        if cv_wrec_eb < 0.1:
            print("   - Wrec/Eb 在同一组内相对稳定 (CV < 0.1)")
        else:
            print("   - Wrec/Eb 在同一组内变化较大 (CV >= 0.1)")

        if cv_wrec_eb2 < 0.1:
            print("   - Wrec/Eb² 在同一组内相对稳定 (CV < 0.1)")
        else:
            print("   - Wrec/Eb² 在同一组内变化较大 (CV >= 0.1)")

    if effect_results:
        print(f"\n2. 成分效应 vs 电场效应 (方差分解分析):")
        for metric, results in effect_results.items():
            comp_ratio = results['component_effect_ratio']
            field_ratio = results['field_effect_ratio']
            print(f"   {metric}:")
            print(f"     - 成分效应占比: {comp_ratio:.2%}")
            print(f"     - 电场效应占比: {field_ratio:.2%}")

            if comp_ratio > field_ratio:
                print(f"     - 结论: {metric} 主要由成分决定")
            else:
                print(f"     - 结论: {metric} 主要由电场决定")

    # 综合分析
    print(f"\n3. 综合结论:")
    if cv_analysis is not None and effect_results:
        print(f"   注意：组内变异系数和方差分解分析从不同角度分析问题：")
        print(f"   - 组内变异系数：衡量同一成分在不同电场下的稳定性")
        print(f"   - 方差分解分析：衡量总体变异中成分和电场的贡献比例")
        print(f"   - 两者结合可以全面理解数据特征")

    return df

if __name__ == "__main__":
    df = main()
